import type { LoadingModalConfig } from '../types'
import { Loader2 } from 'lucide-react'
import React from 'react'
import { BaseModal } from '../BaseModal'

interface LoadingModalProps {
  config: LoadingModalConfig
  visible: boolean
  onClose: () => void
}

export const LoadingModal: React.FC<LoadingModalProps> = ({
  config,
  visible,
  onClose,
}) => {
  const {
    message = 'Đang tải...',
    progress,
    showProgress = false,
  } = config

  return (
    <BaseModal
      config={{
        ...config,
        closable: false,
        maskClosable: false,
        showCloseButton: false,
      }}
      visible={visible}
      onClose={onClose}
    >
      <div className="text-center py-8">
        {/* Loading Spinner */}
        <div className="flex justify-center mb-4">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
        </div>

        {/* Message */}
        <p className="text-gray-700 mb-4">{message}</p>

        {/* Progress Bar */}
        {showProgress && typeof progress === 'number' && (
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
            />
            <p className="text-sm text-gray-500 mt-2">
              {Math.round(progress)}
              %
            </p>
          </div>
        )}
      </div>
    </BaseModal>
  )
}
