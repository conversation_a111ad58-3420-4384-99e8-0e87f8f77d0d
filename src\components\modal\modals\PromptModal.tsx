import type { PromptModalConfig } from '../types'
import { Input } from '@/components/ui'
import { Button } from '@/components/ui/button'
import React, { useState } from 'react'
import { BaseModal } from '../BaseModal'

interface PromptModalProps {
  config: PromptModalConfig
  visible: boolean
  onClose: () => void
}

export const PromptModal: React.FC<PromptModalProps> = ({
  config,
  visible,
  onClose,
}) => {
  const {
    content,
    placeholder = '',
    defaultValue = '',
    inputType = 'text',
    validation,
    onSubmit,
    onCancel,
    submitText = 'Xác nhận',
    cancelText = 'Hủy',
  } = config

  const [value, setValue] = useState(defaultValue)
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)

  const handleSubmit = async () => {
    if (!onSubmit) {
      return
    }

    // Validation
    if (validation) {
      const validationError = validation(value)
      if (validationError) {
        setError(validationError)
        return
      }
    }

    try {
      setLoading(true)
      setError(null)
      await onSubmit(value)
      onClose()
    } catch (error) {
      console.error('Prompt submit failed:', error)
      setError('Đã xảy ra lỗi, vui lòng thử lại')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    }
    onClose()
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && inputType !== 'textarea') {
      handleSubmit()
    }
  }

  return (
    <BaseModal config={config} visible={visible} onClose={onClose}>
      <div>
        {/* Content */}
        {content && (
          <div className="mb-4">
            {typeof content === 'string'
              ? (
                  <p className="text-gray-700">{content}</p>
                )
              : (
                  content
                )}
          </div>
        )}

        {/* Input */}
        <div className="mb-4">
          {inputType === 'textarea'
            ? (
                <textarea
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-vertical"
                  placeholder={placeholder}
                  value={value}
                  onChange={e => setValue(e.target.value)}
                  rows={4}
                  disabled={loading}
                />
              )
            : (
                <Input
                  type={inputType}
                  placeholder={placeholder}
                  value={value}
                  onChange={e => setValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  disabled={loading}
                  className="w-full"
                />
              )}
          {error && (
            <p className="mt-1 text-sm text-red-500">{error}</p>
          )}
        </div>

        {/* Actions */}
        <div className="flex gap-3 justify-end">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={loading}
          >
            {cancelText}
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading}
            loading={loading}
          >
            {submitText}
          </Button>
        </div>
      </div>
    </BaseModal>
  )
}
