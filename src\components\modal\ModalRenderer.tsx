import React from 'react'
import { useModal } from './ModalContext'
import { AlertModal, ConfirmModal } from './modals/ConfirmModal'
import { CustomModal } from './modals/CustomModal'
import { FormModal } from './modals/FormModal'
import { LoadingModal } from './modals/LoadingModal'
import { ImageModal, VideoModal } from './modals/MediaModal'
import { PromptModal } from './modals/PromptModal'

export const ModalRenderer: React.FC = () => {
  const { modals, closeModal } = useModal()

  const renderModal = (modal: any) => {
    const { id, config, visible } = modal
    const onClose = () => closeModal(id)

    switch (config.type) {
      case 'confirm':
        return (
          <ConfirmModal
            key={id}
            config={config}
            visible={visible}
            onClose={onClose}
          />
        )

      case 'alert':
      case 'success':
      case 'error':
      case 'warning':
      case 'info':
        return (
          <AlertModal
            key={id}
            config={config}
            visible={visible}
            onClose={onClose}
          />
        )

      case 'prompt':
        return (
          <PromptModal
            key={id}
            config={config}
            visible={visible}
            onClose={onClose}
          />
        )

      case 'loading':
        return (
          <LoadingModal
            key={id}
            config={config}
            visible={visible}
            onClose={onClose}
          />
        )

      case 'form':
        return (
          <FormModal
            key={id}
            config={config}
            visible={visible}
            onClose={onClose}
          />
        )

      case 'image':
        return (
          <ImageModal
            key={id}
            config={config}
            visible={visible}
            onClose={onClose}
          />
        )

      case 'video':
        return (
          <VideoModal
            key={id}
            config={config}
            visible={visible}
            onClose={onClose}
          />
        )

      case 'custom':
        return (
          <CustomModal
            key={id}
            config={config}
            visible={visible}
            onClose={onClose}
          />
        )

      default:
        console.warn(`Unknown modal type: ${(config as any).type}`)
        return null
    }
  }

  return (
    <>
      {modals.map(renderModal)}
    </>
  )
}
