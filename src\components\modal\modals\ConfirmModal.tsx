import type { ConfirmModalConfig } from '../types'
import { Button } from '@/components/ui/button'
import { AlertTriangle, CheckCircle, Info, XCircle } from 'lucide-react'
import React, { useState } from 'react'
import { BaseModal } from '../BaseModal'

interface ConfirmModalProps {
  config: ConfirmModalConfig
  visible: boolean
  onClose: () => void
}

export const ConfirmModal: React.FC<ConfirmModalProps> = ({
  config,
  visible,
  onClose,
}) => {
  const [loading, setLoading] = useState(false)

  const {
    content,
    confirmText = 'Xác nhận',
    cancelText = 'Hủy',
    onConfirm,
    onCancel,
    confirmButtonProps = {},
    cancelButtonProps = {},
  } = config

  const handleConfirm = async () => {
    if (!onConfirm) {
      return
    }

    try {
      setLoading(true)
      await onConfirm()
      onClose()
    } catch (error) {
      console.error('Confirm action failed:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    }
    onClose()
  }

  return (
    <BaseModal config={config} visible={visible} onClose={onClose}>
      <div className="text-center">
        {/* Icon */}
        <div className="mx-auto flex items-center justify-center w-12 h-12 rounded-full bg-yellow-100 mb-4">
          <AlertTriangle className="w-6 h-6 text-yellow-600" />
        </div>

        {/* Content */}
        <div className="mb-6">
          {typeof content === 'string'
            ? (
                <p className="text-gray-700">{content}</p>
              )
            : (
                content
              )}
        </div>

        {/* Actions */}
        <div className="flex gap-3 justify-center">
          <Button
            variant={cancelButtonProps.variant || 'outline'}
            onClick={handleCancel}
            disabled={loading}
            {...cancelButtonProps}
          >
            {cancelText}
          </Button>
          <Button
            variant={confirmButtonProps.variant || 'default'}
            onClick={handleConfirm}
            disabled={loading}
            loading={loading || confirmButtonProps.loading}
            {...confirmButtonProps}
          >
            {confirmText}
          </Button>
        </div>
      </div>
    </BaseModal>
  )
}

// Alert Modal Component
interface AlertModalProps {
  config: import('../types').AlertModalConfig
  visible: boolean
  onClose: () => void
}

const variantIcons = {
  success: CheckCircle,
  error: XCircle,
  warning: AlertTriangle,
  info: Info,
}

const variantColors = {
  success: 'text-green-600 bg-green-100',
  error: 'text-red-600 bg-red-100',
  warning: 'text-yellow-600 bg-yellow-100',
  info: 'text-blue-600 bg-blue-100',
}

export const AlertModal: React.FC<AlertModalProps> = ({
  config,
  visible,
  onClose,
}) => {
  const {
    content,
    okText = 'OK',
    onOk,
    icon,
    variant = 'info',
  } = config

  const handleOk = () => {
    if (onOk) {
      onOk()
    }
    onClose()
  }

  const IconComponent = variantIcons[variant]

  return (
    <BaseModal config={config} visible={visible} onClose={onClose}>
      <div className="text-center">
        {/* Icon */}
        <div className={`mx-auto flex items-center justify-center w-12 h-12 rounded-full mb-4 ${variantColors[variant]}`}>
          {icon || <IconComponent className="w-6 h-6" />}
        </div>

        {/* Content */}
        <div className="mb-6">
          {typeof content === 'string'
            ? (
                <p className="text-gray-700">{content}</p>
              )
            : (
                content
              )}
        </div>

        {/* Actions */}
        <div className="flex justify-center">
          <Button onClick={handleOk}>
            {okText}
          </Button>
        </div>
      </div>
    </BaseModal>
  )
}
