'use client'

import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON>ider, <PERSON>dal<PERSON><PERSON><PERSON> } from './ModalContext'

interface NextJSModalProviderProps {
  children: React.ReactNode
}

/**
 * Modal Provider tương thích với Next.js App Router
 * Sử dụng 'use client' directive để đảm bảo chạy trên client-side
 */
export const NextJSModalProvider: React.FC<NextJSModalProviderProps> = ({ children }) => {
  return (
    <ModalProvider>
      {children}
      <ModalRenderer />
    </ModalProvider>
  )
}

// Export hook để sử dụng trong components
export { useModal, useModalManager, modalManager } from './ModalContext'

// Export modal utility
export { modal } from './index'
