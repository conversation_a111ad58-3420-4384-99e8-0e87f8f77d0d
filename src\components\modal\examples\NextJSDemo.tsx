'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { modal } from '../index'

/**
 * Demo component cho Next.js
 * Sử dụng 'use client' directive để đảm bảo chạy trên client-side
 */
export const NextJSModalDemo: React.FC = () => {
  // Basic modal examples
  const showConfirmModal = () => {
    modal.confirm({
      title: 'Xác nhận xóa',
      content: 'Bạn có chắc chắn muốn xóa item này? Hành động này không thể hoàn tác.',
      confirmText: 'Xóa',
      cancelText: 'Hủy',
      confirmButtonProps: { variant: 'destructive' },
      onConfirm: async () => {
        // Simulate API call
        const loadingId = modal.loading({ message: 'Đang xóa...' })
        
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        modal.close(loadingId)
        modal.success('<PERSON><PERSON><PERSON> thành công!')
      },
      onCancel: () => {
        modal.info('Đã hủy thao tác')
      }
    })
  }

  const showFormModal = () => {
    modal.form({
      title: 'Thêm sản phẩm mới',
      size: 'lg',
      fields: [
        {
          name: 'name',
          label: 'Tên sản phẩm',
          type: 'text',
          required: true,
          placeholder: 'Nhập tên sản phẩm'
        },
        {
          name: 'price',
          label: 'Giá',
          type: 'number',
          required: true,
          placeholder: '0',
          validation: (value) => {
            const num = Number(value)
            if (isNaN(num) || num <= 0) return 'Giá phải là số dương'
            return null
          }
        },
        {
          name: 'category',
          label: 'Danh mục',
          type: 'select',
          required: true,
          options: [
            { label: 'Điện tử', value: 'electronics' },
            { label: 'Thời trang', value: 'fashion' },
            { label: 'Gia dụng', value: 'home' },
            { label: 'Sách', value: 'books' }
          ]
        },
        {
          name: 'description',
          label: 'Mô tả',
          type: 'textarea',
          placeholder: 'Mô tả chi tiết sản phẩm...',
          rows: 4
        },
        {
          name: 'featured',
          label: 'Sản phẩm nổi bật',
          type: 'checkbox'
        }
      ],
      onSubmit: async (data) => {
        console.log('Form data:', data)
        
        // Simulate API call
        const loadingId = modal.loading({ 
          message: 'Đang thêm sản phẩm...',
          showProgress: true,
          progress: 0
        })

        // Simulate progress
        for (let i = 0; i <= 100; i += 20) {
          await new Promise(resolve => setTimeout(resolve, 200))
          modal.update(loadingId, { progress: i })
        }

        modal.close(loadingId)
        modal.success('Thêm sản phẩm thành công!')
      }
    })
  }

  const showImageModal = () => {
    modal.image({
      src: 'https://picsum.photos/800/600',
      alt: 'Sample Image',
      title: 'Xem ảnh sản phẩm',
      showDownload: true,
      showZoom: true
    })
  }

  const showPromptModal = () => {
    modal.prompt({
      title: 'Đổi tên danh mục',
      content: 'Nhập tên mới cho danh mục:',
      placeholder: 'Tên danh mục...',
      defaultValue: 'Danh mục cũ',
      validation: (value) => {
        if (value.length < 3) return 'Tên danh mục phải có ít nhất 3 ký tự'
        if (value.length > 50) return 'Tên danh mục không được quá 50 ký tự'
        return null
      },
      onSubmit: (value) => {
        modal.success(`Đã đổi tên danh mục thành: "${value}"`)
      }
    })
  }

  const showMultipleModals = () => {
    modal.info('Bước 1: Chuẩn bị dữ liệu')
    
    setTimeout(() => {
      modal.warning('Bước 2: Kiểm tra dữ liệu')
    }, 1000)
    
    setTimeout(() => {
      modal.success('Bước 3: Hoàn thành!')
    }, 2000)
  }

  // Custom component for custom modal
  const CustomContent = ({ data, onClose }: any) => (
    <div className="text-center">
      <h3 className="text-lg font-semibold mb-4">Thông tin chi tiết</h3>
      <div className="space-y-2 text-left">
        <p><strong>ID:</strong> {data.id}</p>
        <p><strong>Tên:</strong> {data.name}</p>
        <p><strong>Email:</strong> {data.email}</p>
        <p><strong>Vai trò:</strong> {data.role}</p>
      </div>
      <div className="mt-6 flex gap-2 justify-center">
        <Button variant="outline" onClick={onClose}>
          Đóng
        </Button>
        <Button onClick={() => {
          modal.success('Đã cập nhật thông tin!')
          onClose()
        }}>
          Cập nhật
        </Button>
      </div>
    </div>
  )

  const showCustomModal = () => {
    modal.custom({
      title: 'Thông tin người dùng',
      component: CustomContent,
      props: {
        data: {
          id: '123',
          name: 'Nguyễn Văn A',
          email: '<EMAIL>',
          role: 'Admin'
        }
      },
      size: 'lg'
    })
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-2">Next.js Modal Demo</h1>
      <p className="text-gray-600 mb-8">
        Demo các loại modal trong Next.js App Router với 'use client' directive
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Basic Modals */}
        <div className="space-y-3">
          <h3 className="font-semibold text-lg">Basic Modals</h3>
          <Button onClick={() => modal.success('Thao tác thành công!')} className="w-full">
            Success Alert
          </Button>
          <Button onClick={() => modal.error('Có lỗi xảy ra!')} variant="destructive" className="w-full">
            Error Alert
          </Button>
          <Button onClick={() => modal.warning('Cảnh báo quan trọng!')} variant="outline" className="w-full">
            Warning Alert
          </Button>
          <Button onClick={() => modal.info('Thông tin hữu ích')} variant="secondary" className="w-full">
            Info Alert
          </Button>
        </div>

        {/* Interactive Modals */}
        <div className="space-y-3">
          <h3 className="font-semibold text-lg">Interactive Modals</h3>
          <Button onClick={showConfirmModal} className="w-full">
            Confirm Modal
          </Button>
          <Button onClick={showPromptModal} className="w-full">
            Prompt Modal
          </Button>
          <Button onClick={showFormModal} className="w-full">
            Form Modal
          </Button>
          <Button onClick={showImageModal} className="w-full">
            Image Modal
          </Button>
        </div>

        {/* Advanced Features */}
        <div className="space-y-3">
          <h3 className="font-semibold text-lg">Advanced Features</h3>
          <Button onClick={showCustomModal} className="w-full">
            Custom Modal
          </Button>
          <Button onClick={showMultipleModals} className="w-full">
            Multiple Modals
          </Button>
          <Button onClick={() => {
            const loadingId = modal.loading({ message: 'Đang tải...', showProgress: true, progress: 0 })
            let progress = 0
            const interval = setInterval(() => {
              progress += 10
              modal.update(loadingId, { progress })
              if (progress >= 100) {
                clearInterval(interval)
                modal.close(loadingId)
                modal.success('Tải hoàn thành!')
              }
            }, 200)
          }} className="w-full">
            Loading with Progress
          </Button>
          <Button onClick={() => modal.closeAll()} variant="outline" className="w-full">
            Close All Modals
          </Button>
        </div>
      </div>

      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-semibold mb-2">💡 Lưu ý cho Next.js:</h4>
        <ul className="text-sm space-y-1 text-blue-800">
          <li>• Component này sử dụng 'use client' directive</li>
          <li>• Modal Manager tự động handle SSR compatibility</li>
          <li>• Có thể sử dụng trong cả App Router và Pages Router</li>
          <li>• Tất cả modal đều type-safe với TypeScript</li>
        </ul>
      </div>
    </div>
  )
}
