'use client'

import type {
  AlertModalConfig,
  ConfirmModalConfig,
  CustomModalConfig,
  FormModalConfig,
  ImageModalConfig,
  LoadingModalConfig,
  ModalConfig,
  ModalContextType,
  ModalState,
  PromptModalConfig,
  VideoModalConfig,
} from './types'
import React, { createContext, useCallback, useContext, useState } from 'react'

const ModalContext = createContext<ModalContextType | null>(null)

export const useModal = () => {
  const context = useContext(ModalContext)
  if (!context) {
    throw new Error('useModal must be used within a ModalProvider')
  }
  return context
}

interface ModalProviderProps {
  children: React.ReactNode
}

export const ModalProvider: React.FC<ModalProviderProps> = ({ children }) => {
  const [modals, setModals] = useState<ModalState[]>([])

  const generateId = useCallback(() => {
    return `modal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }, [])

  const openModal = useCallback((config: ModalConfig): string => {
    const id = config.id || generateId()

    const modalState: ModalState = {
      id,
      config: { ...config, id },
      visible: true,
      loading: false,
    }

    setModals(prev => [...prev, modalState])
    return id
  }, [generateId])

  const closeModal = useCallback((id: string) => {
    setModals((prev) => {
      const modal = prev.find(m => m.id === id)
      if (modal?.config.onClose) {
        modal.config.onClose()
      }

      const newModals = prev.filter(m => m.id !== id)

      // Call onAfterClose after state update
      setTimeout(() => {
        if (modal?.config.onAfterClose) {
          modal.config.onAfterClose()
        }
      }, 0)

      return newModals
    })
  }, [])

  const closeAllModals = useCallback(() => {
    setModals((prev) => {
      prev.forEach((modal) => {
        if (modal.config.onClose) {
          modal.config.onClose()
        }
      })
      return []
    })
  }, [])

  const updateModal = useCallback((id: string, updates: Partial<ModalConfig>) => {
    setModals(prev => prev.map(modal =>
      modal.id === id
        ? { ...modal, config: { ...modal.config, ...updates } }
        : modal,
    ))
  }, [])

  const value: ModalContextType = {
    modals,
    openModal,
    closeModal,
    closeAllModals,
    updateModal,
  }

  return (
    <ModalContext.Provider value={value}>
      {children}
    </ModalContext.Provider>
  )
}

// Global Modal Manager - Singleton pattern
class ModalManager {
  private static instance: ModalManager
  private contextValue: ModalContextType | null = null

  static getInstance(): ModalManager {
    if (!ModalManager.instance) {
      ModalManager.instance = new ModalManager()
    }
    return ModalManager.instance
  }

  setContext(contextValue: ModalContextType) {
    this.contextValue = contextValue
  }

  // Convenience methods for common modal types
  confirm(config: Omit<ConfirmModalConfig, 'type'>): string {
    if (!this.contextValue) {
      throw new Error('Modal context not initialized')
    }
    return this.contextValue.openModal({ ...config, type: 'confirm' })
  }

  alert(config: Omit<AlertModalConfig, 'type'>): string {
    if (!this.contextValue) {
      throw new Error('Modal context not initialized')
    }
    return this.contextValue.openModal({ ...config, type: 'alert' })
  }

  prompt(config: Omit<PromptModalConfig, 'type'>): string {
    if (!this.contextValue) {
      throw new Error('Modal context not initialized')
    }
    return this.contextValue.openModal({ ...config, type: 'prompt' })
  }

  loading(config: Omit<LoadingModalConfig, 'type'>): string {
    if (!this.contextValue) {
      throw new Error('Modal context not initialized')
    }
    return this.contextValue.openModal({ ...config, type: 'loading' })
  }

  success(message: string, options?: Partial<AlertModalConfig>): string {
    return this.alert({
      ...options,
      content: message,
      variant: 'success',
      title: options?.title || 'Thành công',
    })
  }

  error(message: string, options?: Partial<AlertModalConfig>): string {
    return this.alert({
      ...options,
      content: message,
      variant: 'error',
      title: options?.title || 'Lỗi',
    })
  }

  warning(message: string, options?: Partial<AlertModalConfig>): string {
    return this.alert({
      ...options,
      content: message,
      variant: 'warning',
      title: options?.title || 'Cảnh báo',
    })
  }

  info(message: string, options?: Partial<AlertModalConfig>): string {
    return this.alert({
      ...options,
      content: message,
      variant: 'info',
      title: options?.title || 'Thông tin',
    })
  }

  form(config: Omit<FormModalConfig, 'type'>): string {
    if (!this.contextValue) {
      throw new Error('Modal context not initialized')
    }
    return this.contextValue.openModal({ ...config, type: 'form' })
  }

  image(config: Omit<ImageModalConfig, 'type'>): string {
    if (!this.contextValue) {
      throw new Error('Modal context not initialized')
    }
    return this.contextValue.openModal({ ...config, type: 'image' })
  }

  video(config: Omit<VideoModalConfig, 'type'>): string {
    if (!this.contextValue) {
      throw new Error('Modal context not initialized')
    }
    return this.contextValue.openModal({ ...config, type: 'video' })
  }

  custom(config: Omit<CustomModalConfig, 'type'>): string {
    if (!this.contextValue) {
      throw new Error('Modal context not initialized')
    }
    return this.contextValue.openModal({ ...config, type: 'custom' })
  }

  close(id: string) {
    if (!this.contextValue) {
      throw new Error('Modal context not initialized')
    }
    this.contextValue.closeModal(id)
  }

  closeAll() {
    if (!this.contextValue) {
      throw new Error('Modal context not initialized')
    }
    this.contextValue.closeAllModals()
  }

  update(id: string, updates: Partial<ModalConfig>) {
    if (!this.contextValue) {
      throw new Error('Modal context not initialized')
    }
    this.contextValue.updateModal(id, updates)
  }
}

export const modalManager = ModalManager.getInstance()

// Hook to initialize modal manager with context
export const useModalManager = () => {
  const context = useModal()

  React.useEffect(() => {
    modalManager.setContext(context)
  }, [context])

  return modalManager
}
