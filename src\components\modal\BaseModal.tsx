import type { BaseModalConfig, ModalSize } from './types'
import { X } from 'lucide-react'
import React, { useEffect, useState } from 'react'
import { createPortal } from 'react-dom'

// Custom hook for client-side mounting (Next.js SSR compatibility)
const useClientSideMount = () => {
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  return isMounted
}

interface BaseModalProps {
  config: BaseModalConfig
  visible: boolean
  onClose: () => void
  children: React.ReactNode
}

const sizeClasses: Record<ModalSize, string> = {
  xs: 'max-w-xs',
  sm: 'max-w-sm',
  md: 'max-w-md',
  lg: 'max-w-lg',
  xl: 'max-w-xl',
  full: 'max-w-full w-full h-full',
}

export const BaseModal: React.FC<BaseModalProps> = ({
  config,
  visible,
  onClose,
  children,
}) => {
  const isMounted = useClientSideMount()

  const {
    title,
    size = 'md',
    closable = true,
    maskClosable = true,
    showCloseButton = true,
    className = '',
    style = {},
    zIndex = 1000,
  } = config

  // Handle ESC key
  useEffect(() => {
    if (!isMounted) {
      return
    }

    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && closable) {
        onClose()
      }
    }

    if (visible) {
      document.addEventListener('keydown', handleEsc)
      // Prevent body scroll
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEsc)
      // Restore body scroll if no other modals
      const modals = document.querySelectorAll('[data-modal]')
      if (modals.length <= 1) {
        document.body.style.overflow = ''
      }
    }
  }, [visible, closable, onClose, isMounted])

  // Don't render on server or if not mounted
  if (!isMounted || !visible) {
    return null
  }

  const handleMaskClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && maskClosable && closable) {
      onClose()
    }
  }

  const modalContent = (
    <div
      className="fixed inset-0 flex items-center justify-center p-4"
      style={{ zIndex }}
      onClick={handleMaskClick}
      data-modal
    >
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" />

      {/* Modal */}
      <div
        className={`
          relative bg-white rounded-lg shadow-xl
          ${sizeClasses[size]}
          ${size === 'full' ? '' : 'max-h-[90vh]'}
          ${className}
        `}
        style={style}
        onClick={e => e.stopPropagation()}
      >
        {/* Header */}
        {(title || showCloseButton) && (
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            {title && (
              <h2 className="text-lg font-semibold text-gray-900">
                {title}
              </h2>
            )}
            {showCloseButton && closable && (
              <button
                type="button"
                onClick={onClose}
                className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                aria-label="Close modal"
              >
                <X size={20} />
              </button>
            )}
          </div>
        )}

        {/* Content */}
        <div className={`
          ${title || showCloseButton ? 'p-6' : 'p-6'}
          ${size === 'full' ? 'flex-1 overflow-auto' : 'max-h-[calc(90vh-8rem)] overflow-auto'}
        `}
        >
          {children}
        </div>
      </div>
    </div>
  )

  // Render to portal
  return createPortal(modalContent, document.body)
}

// Animation wrapper component
export const AnimatedModal: React.FC<BaseModalProps> = (props) => {
  const [mounted, setMounted] = React.useState(false)
  const [animating, setAnimating] = React.useState(false)

  React.useEffect(() => {
    if (props.visible) {
      setMounted(true)
      setTimeout(() => setAnimating(true), 10)
    } else {
      setAnimating(false)
      setTimeout(() => setMounted(false), 200)
    }
  }, [props.visible])

  if (!mounted) {
    return null
  }

  return (
    <div
      className={`
        transition-all duration-200 ease-out
        ${animating ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}
      `}
    >
      <BaseModal {...props} />
    </div>
  )
}
