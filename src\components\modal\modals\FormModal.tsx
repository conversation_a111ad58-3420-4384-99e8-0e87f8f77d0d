import type { FormField, FormModalConfig } from '../types'
import { Input } from '@/components/ui'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import React, { useState } from 'react'
import { BaseModal } from '../BaseModal'

interface FormModalProps {
  config: FormModalConfig
  visible: boolean
  onClose: () => void
}

export const FormModal: React.FC<FormModalProps> = ({
  config,
  visible,
  onClose,
}) => {
  const {
    fields,
    onSubmit,
    onCancel,
    submitText = 'Xác nhận',
    cancelText = 'Hủy',
    initialValues = {},
  } = config

  const [formData, setFormData] = useState<Record<string, any>>(initialValues)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [loading, setLoading] = useState(false)

  const handleFieldChange = (name: string, value: any) => {
    setFormData(prev => ({ ...prev, [name]: value }))
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    fields.forEach((field) => {
      const value = formData[field.name]

      // Required validation
      if (field.required && (!value || value === '')) {
        newErrors[field.name] = `${field.label} là bắt buộc`
        return
      }

      // Custom validation
      if (field.validation && value) {
        const error = field.validation(value)
        if (error) {
          newErrors[field.name] = error
        }
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async () => {
    if (!validateForm() || !onSubmit) {
      return
    }

    try {
      setLoading(true)
      await onSubmit(formData)
      onClose()
    } catch (error) {
      console.error('Form submit failed:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    }
    onClose()
  }

  const renderField = (field: FormField) => {
    const value = formData[field.name] || ''
    const error = errors[field.name]

    switch (field.type) {
      case 'textarea':
        return (
          <div key={field.name} className="mb-4">
            <label className="block text-sm font-medium mb-1 text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <textarea
              className={`w-full p-3 border rounded-md transition-colors resize-vertical ${
                error ? 'border-red-500' : 'border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
              }`}
              placeholder={field.placeholder}
              value={value}
              onChange={e => handleFieldChange(field.name, e.target.value)}
              rows={field.rows || 3}
              disabled={loading}
            />
            {error && <p className="mt-1 text-sm text-red-500">{error}</p>}
          </div>
        )

      case 'select':
        return (
          <div key={field.name} className="mb-4">
            <label className="block text-sm font-medium mb-1 text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <select
              className={`w-full p-3 border rounded-md transition-colors ${
                error ? 'border-red-500' : 'border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
              }`}
              value={value}
              onChange={e => handleFieldChange(field.name, e.target.value)}
              disabled={loading}
            >
              <option value="">{field.placeholder || `Chọn ${field.label}`}</option>
              {field.options?.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {error && <p className="mt-1 text-sm text-red-500">{error}</p>}
          </div>
        )

      case 'checkbox':
        return (
          <div key={field.name} className="mb-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id={field.name}
                checked={!!value}
                onCheckedChange={checked => handleFieldChange(field.name, checked)}
                disabled={loading}
              />
              <label htmlFor={field.name} className="text-sm text-gray-700">
                {field.label}
                {field.required && <span className="text-red-500 ml-1">*</span>}
              </label>
            </div>
            {error && <p className="mt-1 text-sm text-red-500">{error}</p>}
          </div>
        )

      case 'radio':
        return (
          <div key={field.name} className="mb-4">
            <label className="block text-sm font-medium mb-2 text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <div className="space-y-2">
              {field.options?.map(option => (
                <div key={option.value} className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id={`${field.name}-${option.value}`}
                    name={field.name}
                    value={option.value}
                    checked={value === option.value}
                    onChange={e => handleFieldChange(field.name, e.target.value)}
                    disabled={loading}
                    className="text-blue-600"
                  />
                  <label htmlFor={`${field.name}-${option.value}`} className="text-sm text-gray-700">
                    {option.label}
                  </label>
                </div>
              ))}
            </div>
            {error && <p className="mt-1 text-sm text-red-500">{error}</p>}
          </div>
        )

      default:
        return (
          <div key={field.name} className="mb-4">
            <label className="block text-sm font-medium mb-1 text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <Input
              type={field.type}
              placeholder={field.placeholder}
              value={value}
              onChange={e => handleFieldChange(field.name, e.target.value)}
              disabled={loading}
              className={error ? 'border-red-500' : ''}
            />
            {error && <p className="mt-1 text-sm text-red-500">{error}</p>}
          </div>
        )
    }
  }

  return (
    <BaseModal config={config} visible={visible} onClose={onClose}>
      <div>
        {/* Form Fields */}
        <div className="max-h-96 overflow-y-auto">
          {fields.map(renderField)}
        </div>

        {/* Actions */}
        <div className="flex gap-3 justify-end pt-4 border-t border-gray-200">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={loading}
          >
            {cancelText}
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading}
            loading={loading}
          >
            {submitText}
          </Button>
        </div>
      </div>
    </BaseModal>
  )
}
