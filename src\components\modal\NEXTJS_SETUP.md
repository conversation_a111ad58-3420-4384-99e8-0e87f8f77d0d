# Next.js Modal Manager Setup

Hướng dẫn setup Modal Manager cho Next.js App Router và Pages Router.

## 🚀 Setup cho Next.js App Router (Recommended)

### 1. Tạo Root Layout

```tsx
// app/layout.tsx
import { NextJSModalProvider } from '@/components/modal/NextJSModalProvider'
import './globals.css'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="vi">
      <body>
        <NextJSModalProvider>
          {children}
        </NextJSModalProvider>
      </body>
    </html>
  )
}
```

### 2. Sử dụng trong Server Components

```tsx
// app/page.tsx (Server Component)
import { ClientModalDemo } from './ClientModalDemo'

export default function HomePage() {
  return (
    <div>
      <h1>Server Component</h1>
      <ClientModalDemo />
    </div>
  )
}
```

### 3. Tạo Client Component để sử dụng Modal

```tsx
// app/ClientModalDemo.tsx
'use client'

import { modal } from '@/components/modal/NextJSModalProvider'
import { Button } from '@/components/ui/button'

export function ClientModalDemo() {
  const handleShowModal = () => {
    modal.confirm({
      title: 'Xác nhận',
      content: 'Bạn có chắc chắn muốn thực hiện hành động này?',
      onConfirm: () => {
        modal.success('Thành công!')
      }
    })
  }

  return (
    <Button onClick={handleShowModal}>
      Show Modal
    </Button>
  )
}
```

## 📱 Setup cho Next.js Pages Router

### 1. Wrap App với Provider

```tsx
// pages/_app.tsx
import type { AppProps } from 'next/app'
import { NextJSModalProvider } from '@/components/modal/NextJSModalProvider'
import '@/styles/globals.css'

export default function App({ Component, pageProps }: AppProps) {
  return (
    <NextJSModalProvider>
      <Component {...pageProps} />
    </NextJSModalProvider>
  )
}
```

### 2. Sử dụng trong Pages

```tsx
// pages/index.tsx
import { modal } from '@/components/modal/NextJSModalProvider'
import { Button } from '@/components/ui/button'

export default function HomePage() {
  const handleShowModal = () => {
    modal.alert({
      title: 'Thông báo',
      content: 'Đây là modal từ Pages Router!',
      variant: 'info'
    })
  }

  return (
    <div>
      <h1>Pages Router</h1>
      <Button onClick={handleShowModal}>
        Show Modal
      </Button>
    </div>
  )
}
```

## 🔧 Advanced Usage

### 1. Custom Hook cho Modal

```tsx
// hooks/useAppModal.ts
'use client'

import { modal } from '@/components/modal/NextJSModalProvider'
import { useCallback } from 'react'

export const useAppModal = () => {
  const showDeleteConfirm = useCallback((onConfirm: () => void) => {
    modal.confirm({
      title: 'Xác nhận xóa',
      content: 'Hành động này không thể hoàn tác. Bạn có chắc chắn?',
      confirmText: 'Xóa',
      cancelText: 'Hủy',
      confirmButtonProps: { variant: 'destructive' },
      onConfirm
    })
  }, [])

  const showSuccessMessage = useCallback((message: string) => {
    modal.success(message)
  }, [])

  const showErrorMessage = useCallback((message: string) => {
    modal.error(message)
  }, [])

  return {
    showDeleteConfirm,
    showSuccessMessage,
    showErrorMessage,
    modal // Expose full modal API
  }
}
```

### 2. Sử dụng Custom Hook

```tsx
// components/UserList.tsx
'use client'

import { useAppModal } from '@/hooks/useAppModal'
import { Button } from '@/components/ui/button'

export function UserList() {
  const { showDeleteConfirm, showSuccessMessage } = useAppModal()

  const handleDelete = (userId: string) => {
    showDeleteConfirm(async () => {
      try {
        await deleteUser(userId)
        showSuccessMessage('Xóa người dùng thành công!')
      } catch (error) {
        showErrorMessage('Có lỗi xảy ra khi xóa người dùng')
      }
    })
  }

  return (
    <div>
      {/* User list */}
      <Button onClick={() => handleDelete('123')}>
        Xóa User
      </Button>
    </div>
  )
}
```

### 3. Form Modal với React Hook Form

```tsx
// components/AddUserModal.tsx
'use client'

import { modal } from '@/components/modal/NextJSModalProvider'
import { useForm } from 'react-hook-form'

interface UserFormData {
  name: string
  email: string
  role: string
}

export const useAddUserModal = () => {
  const showAddUserModal = () => {
    modal.form({
      title: 'Thêm người dùng mới',
      size: 'lg',
      fields: [
        {
          name: 'name',
          label: 'Họ tên',
          type: 'text',
          required: true,
          placeholder: 'Nhập họ tên'
        },
        {
          name: 'email',
          label: 'Email',
          type: 'email',
          required: true,
          placeholder: '<EMAIL>',
          validation: (value) => {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
            return emailRegex.test(value) ? null : 'Email không hợp lệ'
          }
        },
        {
          name: 'role',
          label: 'Vai trò',
          type: 'select',
          required: true,
          options: [
            { label: 'Admin', value: 'admin' },
            { label: 'User', value: 'user' },
            { label: 'Guest', value: 'guest' }
          ]
        }
      ],
      onSubmit: async (data: UserFormData) => {
        try {
          await createUser(data)
          modal.success('Thêm người dùng thành công!')
        } catch (error) {
          modal.error('Có lỗi xảy ra khi thêm người dùng')
        }
      }
    })
  }

  return { showAddUserModal }
}
```

## 🎯 Best Practices

### 1. Tách Modal Logic

```tsx
// utils/modalHelpers.ts
import { modal } from '@/components/modal/NextJSModalProvider'

export const confirmDelete = (itemName: string, onConfirm: () => void) => {
  modal.confirm({
    title: 'Xác nhận xóa',
    content: `Bạn có chắc chắn muốn xóa "${itemName}"? Hành động này không thể hoàn tác.`,
    confirmText: 'Xóa',
    cancelText: 'Hủy',
    confirmButtonProps: { variant: 'destructive' },
    onConfirm
  })
}

export const showApiError = (error: any) => {
  const message = error?.response?.data?.message || 'Có lỗi xảy ra'
  modal.error(message)
}

export const showLoadingModal = (message: string = 'Đang xử lý...') => {
  return modal.loading({ message })
}
```

### 2. Type-Safe Modal Configs

```tsx
// types/modal.ts
import type { ConfirmModalConfig } from '@/components/modal'

export interface DeleteConfirmConfig extends Omit<ConfirmModalConfig, 'type'> {
  itemName: string
  itemType?: string
}

export const createDeleteModal = (config: DeleteConfirmConfig) => {
  return modal.confirm({
    title: `Xác nhận xóa ${config.itemType || 'item'}`,
    content: `Bạn có chắc chắn muốn xóa "${config.itemName}"?`,
    confirmText: 'Xóa',
    cancelText: 'Hủy',
    confirmButtonProps: { variant: 'destructive' },
    ...config
  })
}
```

## ⚠️ Lưu ý quan trọng

1. **'use client' directive**: Bắt buộc cho App Router
2. **SSR Compatibility**: Modal chỉ render trên client-side
3. **Performance**: Sử dụng React.memo cho heavy components
4. **Accessibility**: Modal tự động handle ESC key và focus management
5. **Z-index**: Default là 1000, có thể tùy chỉnh nếu cần

## 🐛 Troubleshooting

### Modal không hiển thị
- Kiểm tra `NextJSModalProvider` đã được wrap đúng chưa
- Đảm bảo component sử dụng modal có `'use client'`

### Hydration mismatch
- Modal sử dụng `useClientSideMount` để tránh SSR issues
- Không render modal content trên server

### TypeScript errors
- Import đúng types từ `@/components/modal`
- Sử dụng generic types cho custom modal configs
