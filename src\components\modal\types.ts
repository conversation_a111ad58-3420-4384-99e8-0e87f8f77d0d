// Modal Types và Interfaces

export type ModalType =
  | 'confirm'
  | 'alert'
  | 'prompt'
  | 'custom'
  | 'loading'
  | 'success'
  | 'error'
  | 'warning'
  | 'info'
  | 'form'
  | 'image'
  | 'video'

export type ModalSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full'

export interface BaseModalConfig {
  id?: string
  type: ModalType
  title?: string
  content?: React.ReactNode | string
  size?: ModalSize
  closable?: boolean
  maskClosable?: boolean
  showCloseButton?: boolean
  className?: string
  style?: React.CSSProperties
  zIndex?: number
  onClose?: () => void
  onAfterClose?: () => void
}

export interface ConfirmModalConfig extends BaseModalConfig {
  type: 'confirm'
  confirmText?: string
  cancelText?: string
  onConfirm?: () => void | Promise<void>
  onCancel?: () => void
  confirmButtonProps?: {
    variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
    loading?: boolean
  }
  cancelButtonProps?: {
    variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  }
}

export interface AlertModalConfig extends BaseModalConfig {
  type: 'alert'
  okText?: string
  onOk?: () => void
  icon?: React.ReactNode
  variant?: 'success' | 'error' | 'warning' | 'info'
}

export interface PromptModalConfig extends BaseModalConfig {
  type: 'prompt'
  placeholder?: string
  defaultValue?: string
  inputType?: 'text' | 'email' | 'password' | 'number' | 'textarea'
  validation?: (value: string) => string | null
  onSubmit?: (value: string) => void | Promise<void>
  onCancel?: () => void
  submitText?: string
  cancelText?: string
}

export interface LoadingModalConfig extends BaseModalConfig {
  type: 'loading'
  message?: string
  progress?: number
  showProgress?: boolean
}

export interface FormModalConfig extends BaseModalConfig {
  type: 'form'
  fields: FormField[]
  onSubmit?: (data: Record<string, any>) => void | Promise<void>
  onCancel?: () => void
  submitText?: string
  cancelText?: string
  initialValues?: Record<string, any>
}

export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'date'
  placeholder?: string
  required?: boolean
  validation?: (value: any) => string | null
  options?: { label: string, value: any }[] // For select, radio
  rows?: number // For textarea
}

export interface ImageModalConfig extends BaseModalConfig {
  type: 'image'
  src: string
  alt?: string
  showDownload?: boolean
  showZoom?: boolean
}

export interface VideoModalConfig extends BaseModalConfig {
  type: 'video'
  src: string
  poster?: string
  autoplay?: boolean
  controls?: boolean
}

export interface CustomModalConfig extends BaseModalConfig {
  type: 'custom'
  component: React.ComponentType<any>
  props?: Record<string, any>
}

export type ModalConfig =
  | ConfirmModalConfig
  | AlertModalConfig
  | PromptModalConfig
  | LoadingModalConfig
  | FormModalConfig
  | ImageModalConfig
  | VideoModalConfig
  | CustomModalConfig

export interface ModalState {
  id: string
  config: ModalConfig
  visible: boolean
  loading?: boolean
}

export interface ModalContextType {
  modals: ModalState[]
  openModal: (config: ModalConfig) => string
  closeModal: (id: string) => void
  closeAllModals: () => void
  updateModal: (id: string, updates: Partial<ModalConfig>) => void
}
