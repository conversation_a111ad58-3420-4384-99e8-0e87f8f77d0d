import type { ImageModalConfig, VideoModalConfig } from '../types'
import { Button } from '@/components/ui/button'
import { Download, RotateCw, ZoomIn, ZoomOut } from 'lucide-react'
import React, { useState } from 'react'
import { BaseModal } from '../BaseModal'

// Image Modal
interface ImageModalProps {
  config: ImageModalConfig
  visible: boolean
  onClose: () => void
}

export const ImageModal: React.FC<ImageModalProps> = ({
  config,
  visible,
  onClose,
}) => {
  const {
    src,
    alt = 'Image',
    showDownload = true,
    showZoom = true,
  } = config

  const [zoom, setZoom] = useState(1)
  const [rotation, setRotation] = useState(0)

  const handleDownload = () => {
    const link = document.createElement('a')
    link.href = src
    link.download = alt || 'image'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handleZoomIn = () => setZoom(prev => Math.min(prev + 0.25, 3))
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 0.25, 0.25))
  const handleRotate = () => setRotation(prev => (prev + 90) % 360)
  const handleReset = () => {
    setZoom(1)
    setRotation(0)
  }

  return (
    <BaseModal
      config={{
        ...config,
        size: 'xl',
        className: 'bg-black',
      }}
      visible={visible}
      onClose={onClose}
    >
      <div className="relative">
        {/* Controls */}
        <div className="absolute top-4 right-4 z-10 flex gap-2">
          {showZoom && (
            <>
              <Button
                size="sm"
                variant="secondary"
                onClick={handleZoomOut}
                disabled={zoom <= 0.25}
              >
                <ZoomOut size={16} />
              </Button>
              <Button
                size="sm"
                variant="secondary"
                onClick={handleZoomIn}
                disabled={zoom >= 3}
              >
                <ZoomIn size={16} />
              </Button>
              <Button
                size="sm"
                variant="secondary"
                onClick={handleRotate}
              >
                <RotateCw size={16} />
              </Button>
              <Button
                size="sm"
                variant="secondary"
                onClick={handleReset}
              >
                Reset
              </Button>
            </>
          )}
          {showDownload && (
            <Button
              size="sm"
              variant="secondary"
              onClick={handleDownload}
            >
              <Download size={16} />
            </Button>
          )}
        </div>

        {/* Image */}
        <div className="flex items-center justify-center min-h-[400px] overflow-hidden">
          <img
            src={src}
            alt={alt}
            className="max-w-full max-h-[80vh] object-contain transition-transform duration-200"
            style={{
              transform: `scale(${zoom}) rotate(${rotation}deg)`,
            }}
          />
        </div>

        {/* Zoom indicator */}
        {showZoom && (
          <div className="absolute bottom-4 left-4 bg-black/50 text-white px-2 py-1 rounded text-sm">
            {Math.round(zoom * 100)}
            %
          </div>
        )}
      </div>
    </BaseModal>
  )
}

// Video Modal
interface VideoModalProps {
  config: VideoModalConfig
  visible: boolean
  onClose: () => void
}

export const VideoModal: React.FC<VideoModalProps> = ({
  config,
  visible,
  onClose,
}) => {
  const {
    src,
    poster,
    autoplay = false,
    controls = true,
  } = config

  return (
    <BaseModal
      config={{
        ...config,
        size: 'xl',
      }}
      visible={visible}
      onClose={onClose}
    >
      <div className="relative">
        <video
          src={src}
          poster={poster}
          controls={controls}
          autoPlay={autoplay}
          className="w-full max-h-[80vh]"
          onLoadStart={() => {
            // Optional: Add loading state
          }}
        >
          Trình duyệt của bạn không hỗ trợ video.
        </video>
      </div>
    </BaseModal>
  )
}
