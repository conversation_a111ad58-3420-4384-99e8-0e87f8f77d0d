# Modal Manager System

Hệ thống quản lý modal toàn cục cho React application. Cho phép gọi modal từ bất kỳ đâu trong ứng dụng với cú pháp đơn giản.

## Setup

### 1. Wrap ứng dụng với <PERSON>

```tsx
// app/layout.tsx hoặc _app.tsx
import { ModalProvider, ModalRenderer } from '@/components/modal'

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html>
      <body>
        <ModalProvider>
          {children}
          <ModalRenderer />
        </ModalProvider>
      </body>
    </html>
  )
}
```

### 2. Sử dụng trong component

```tsx
import { modal } from '@/components/modal'

function MyComponent() {
  const handleDelete = () => {
    modal.confirm({
      title: 'Xác nhận xóa',
      content: 'Bạn có chắc chắn muốn xóa item này?',
      onConfirm: async () => {
        await deleteItem()
        modal.success('Xóa thành công!')
      }
    })
  }

  return <button onClick={handleDelete}>Xóa</button>
}
```

## Các loại Modal

### 1. Confirm Modal
```tsx
modal.confirm({
  title: 'Xác nhận',
  content: 'Bạn có chắc chắn?',
  confirmText: 'Đồng ý',
  cancelText: 'Hủy',
  onConfirm: () => console.log('Confirmed'),
  onCancel: () => console.log('Cancelled'),
  confirmButtonProps: { variant: 'destructive' }
})
```

### 2. Alert Modals
```tsx
// Quick alerts
modal.success('Thành công!')
modal.error('Có lỗi xảy ra!')
modal.warning('Cảnh báo!')
modal.info('Thông tin')

// Custom alert
modal.alert({
  title: 'Thông báo',
  content: 'Nội dung thông báo',
  variant: 'success',
  onOk: () => console.log('OK clicked')
})
```

### 3. Prompt Modal
```tsx
modal.prompt({
  title: 'Nhập tên',
  content: 'Vui lòng nhập tên của bạn:',
  placeholder: 'Tên...',
  inputType: 'text',
  validation: (value) => value.length < 2 ? 'Tên phải có ít nhất 2 ký tự' : null,
  onSubmit: (value) => console.log('Name:', value)
})
```

### 4. Loading Modal
```tsx
const loadingId = modal.loading({
  message: 'Đang tải dữ liệu...',
  showProgress: true,
  progress: 0
})

// Update progress
modal.update(loadingId, { progress: 50 })

// Close when done
modal.close(loadingId)
```

### 5. Form Modal
```tsx
modal.form({
  title: 'Thêm người dùng',
  fields: [
    {
      name: 'name',
      label: 'Họ tên',
      type: 'text',
      required: true,
      placeholder: 'Nhập họ tên'
    },
    {
      name: 'email',
      label: 'Email',
      type: 'email',
      required: true,
      validation: (value) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        return emailRegex.test(value) ? null : 'Email không hợp lệ'
      }
    },
    {
      name: 'role',
      label: 'Vai trò',
      type: 'select',
      required: true,
      options: [
        { label: 'Admin', value: 'admin' },
        { label: 'User', value: 'user' }
      ]
    }
  ],
  onSubmit: (data) => {
    console.log('Form data:', data)
    // Handle form submission
  }
})
```

### 6. Image Modal
```tsx
modal.image({
  src: '/path/to/image.jpg',
  alt: 'Image description',
  showDownload: true,
  showZoom: true
})
```

### 7. Video Modal
```tsx
modal.video({
  src: '/path/to/video.mp4',
  poster: '/path/to/poster.jpg',
  autoplay: false,
  controls: true
})
```

### 8. Custom Modal
```tsx
const MyCustomComponent = ({ data, onClose }) => (
  <div>
    <h3>Custom Content</h3>
    <p>{data.message}</p>
    <button onClick={onClose}>Close</button>
  </div>
)

modal.custom({
  title: 'Custom Modal',
  component: MyCustomComponent,
  props: {
    data: { message: 'Hello World' }
  },
  size: 'lg'
})
```

## API Reference

### Modal Sizes
- `xs`: 320px
- `sm`: 384px  
- `md`: 448px (default)
- `lg`: 512px
- `xl`: 576px
- `full`: Full screen

### Common Options
```tsx
{
  id?: string,              // Custom ID
  title?: string,           // Modal title
  size?: ModalSize,         // Modal size
  closable?: boolean,       // Can close (default: true)
  maskClosable?: boolean,   // Click outside to close (default: true)
  showCloseButton?: boolean, // Show X button (default: true)
  className?: string,       // Custom CSS class
  style?: CSSProperties,    // Custom styles
  zIndex?: number,          // Z-index (default: 1000)
  onClose?: () => void,     // Close callback
  onAfterClose?: () => void // After close callback
}
```

### Control Methods
```tsx
// Close specific modal
modal.close(modalId)

// Close all modals
modal.closeAll()

// Update modal
modal.update(modalId, { title: 'New Title' })
```

## Advanced Usage

### Using with hooks
```tsx
import { useModal, useModalManager } from '@/components/modal'

function MyComponent() {
  const { openModal, closeModal } = useModal()
  const modalManager = useModalManager()

  const handleCustomModal = () => {
    const id = openModal({
      type: 'custom',
      component: MyComponent,
      props: { data: 'test' }
    })
    
    // Close after 5 seconds
    setTimeout(() => closeModal(id), 5000)
  }
}
```

### Promise-based usage
```tsx
const confirmed = await new Promise((resolve) => {
  modal.confirm({
    title: 'Confirm',
    content: 'Are you sure?',
    onConfirm: () => resolve(true),
    onCancel: () => resolve(false)
  })
})

if (confirmed) {
  // Do something
}
```
