import React from 'react'
import { Button } from '@/components/ui/button'
import { modal } from '../index'

export const ModalExamples: React.FC = () => {
  // Basic Alerts
  const showSuccessAlert = () => {
    modal.success('Thao tác thành công!')
  }

  const showErrorAlert = () => {
    modal.error('Đã xảy ra lỗi!')
  }

  const showWarningAlert = () => {
    modal.warning('Cảnh báo: Dữ liệu sẽ bị mất!')
  }

  const showInfoAlert = () => {
    modal.info('Thông tin: Hệ thống sẽ bảo trì vào 2h sáng')
  }

  // Confirm Modal
  const showConfirmModal = () => {
    modal.confirm({
      title: 'Xác nhận xóa',
      content: 'Bạn có chắc chắn muốn xóa item này? Hành động này không thể hoàn tác.',
      confirmText: 'Xóa',
      cancelText: 'Hủy',
      confirmButtonProps: { variant: 'destructive' },
      onConfirm: async () => {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000))
        modal.success('Xóa thành công!')
      },
      onCancel: () => {
        modal.info('Đã hủy thao tác')
      }
    })
  }

  // Prompt Modal
  const showPromptModal = () => {
    modal.prompt({
      title: 'Đổi tên',
      content: 'Nhập tên mới cho item:',
      placeholder: 'Tên mới...',
      defaultValue: 'Item cũ',
      validation: (value) => {
        if (value.length < 3) return 'Tên phải có ít nhất 3 ký tự'
        if (value.length > 50) return 'Tên không được quá 50 ký tự'
        return null
      },
      onSubmit: (value) => {
        modal.success(`Đã đổi tên thành: ${value}`)
      }
    })
  }

  // Loading Modal
  const showLoadingModal = () => {
    const loadingId = modal.loading({
      message: 'Đang tải dữ liệu...',
      showProgress: true,
      progress: 0
    })

    // Simulate progress
    let progress = 0
    const interval = setInterval(() => {
      progress += 10
      modal.update(loadingId, { 
        progress,
        message: `Đang tải... ${progress}%`
      })

      if (progress >= 100) {
        clearInterval(interval)
        modal.close(loadingId)
        modal.success('Tải dữ liệu thành công!')
      }
    }, 200)
  }

  // Form Modal
  const showFormModal = () => {
    modal.form({
      title: 'Thêm người dùng mới',
      fields: [
        {
          name: 'name',
          label: 'Họ tên',
          type: 'text',
          required: true,
          placeholder: 'Nhập họ tên'
        },
        {
          name: 'email',
          label: 'Email',
          type: 'email',
          required: true,
          placeholder: '<EMAIL>',
          validation: (value) => {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
            return emailRegex.test(value) ? null : 'Email không hợp lệ'
          }
        },
        {
          name: 'role',
          label: 'Vai trò',
          type: 'select',
          required: true,
          options: [
            { label: 'Quản trị viên', value: 'admin' },
            { label: 'Người dùng', value: 'user' },
            { label: 'Khách', value: 'guest' }
          ]
        },
        {
          name: 'bio',
          label: 'Giới thiệu',
          type: 'textarea',
          placeholder: 'Viết vài dòng về bản thân...',
          rows: 3
        },
        {
          name: 'newsletter',
          label: 'Đăng ký nhận tin tức',
          type: 'checkbox'
        }
      ],
      onSubmit: (data) => {
        console.log('Form data:', data)
        modal.success('Thêm người dùng thành công!')
      }
    })
  }

  // Image Modal
  const showImageModal = () => {
    modal.image({
      src: 'https://picsum.photos/800/600',
      alt: 'Sample Image',
      title: 'Xem ảnh',
      showDownload: true,
      showZoom: true
    })
  }

  // Custom Modal
  const CustomComponent = ({ data, onClose }: any) => (
    <div className="text-center">
      <h3 className="text-lg font-semibold mb-4">Custom Modal Content</h3>
      <p className="text-gray-600 mb-4">{data.message}</p>
      <div className="space-y-2">
        <p>Đây là nội dung tùy chỉnh</p>
        <p>Bạn có thể render bất kỳ component nào ở đây</p>
      </div>
      <Button onClick={onClose} className="mt-4">
        Đóng
      </Button>
    </div>
  )

  const showCustomModal = () => {
    modal.custom({
      title: 'Modal tùy chỉnh',
      component: CustomComponent,
      props: {
        data: { message: 'Hello from custom modal!' }
      },
      size: 'lg'
    })
  }

  // Multiple Modals
  const showMultipleModals = () => {
    modal.info('Modal đầu tiên')
    
    setTimeout(() => {
      modal.warning('Modal thứ hai')
    }, 1000)
    
    setTimeout(() => {
      modal.success('Modal thứ ba')
    }, 2000)
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-8">Modal Examples</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Basic Alerts */}
        <div className="space-y-2">
          <h3 className="font-semibold">Basic Alerts</h3>
          <Button onClick={showSuccessAlert} variant="default" className="w-full">
            Success Alert
          </Button>
          <Button onClick={showErrorAlert} variant="destructive" className="w-full">
            Error Alert
          </Button>
          <Button onClick={showWarningAlert} variant="outline" className="w-full">
            Warning Alert
          </Button>
          <Button onClick={showInfoAlert} variant="secondary" className="w-full">
            Info Alert
          </Button>
        </div>

        {/* Interactive Modals */}
        <div className="space-y-2">
          <h3 className="font-semibold">Interactive Modals</h3>
          <Button onClick={showConfirmModal} className="w-full">
            Confirm Modal
          </Button>
          <Button onClick={showPromptModal} className="w-full">
            Prompt Modal
          </Button>
          <Button onClick={showLoadingModal} className="w-full">
            Loading Modal
          </Button>
          <Button onClick={showFormModal} className="w-full">
            Form Modal
          </Button>
        </div>

        {/* Advanced Modals */}
        <div className="space-y-2">
          <h3 className="font-semibold">Advanced Modals</h3>
          <Button onClick={showImageModal} className="w-full">
            Image Modal
          </Button>
          <Button onClick={showCustomModal} className="w-full">
            Custom Modal
          </Button>
          <Button onClick={showMultipleModals} className="w-full">
            Multiple Modals
          </Button>
          <Button onClick={() => modal.closeAll()} variant="outline" className="w-full">
            Close All Modals
          </Button>
        </div>
      </div>
    </div>
  )
}
