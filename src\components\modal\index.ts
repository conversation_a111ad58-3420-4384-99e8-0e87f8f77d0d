import type {
  AlertModalConfig,
  ConfirmModalConfig,
  CustomModalConfig,
  FormModalConfig,
  ImageModalConfig,
  LoadingModalConfig,
  ModalConfig,
  PromptModalConfig,
  VideoModalConfig,
} from './types'
// Export components
// Import modalManager for convenience functions
import { modalManager } from './ModalContext'

export { BaseModal } from './BaseModal'

// Export context and hooks
export { modalManager, ModalProvider, useModal, useModalManager } from './ModalContext'

export { ModalRenderer } from './ModalRenderer'

// Export individual modal components
export { AlertModal, ConfirmModal } from './modals/ConfirmModal'
export { CustomModal } from './modals/CustomModal'

export { FormModal } from './modals/FormModal'
export { LoadingModal } from './modals/LoadingModal'
export { ImageModal, VideoModal } from './modals/MediaModal'
export { PromptModal } from './modals/PromptModal'
// Export Next.js specific provider
export { NextJSModalProvider } from './NextJSModalProvider'
// Export types
export type * from './types'

// Convenience function for global usage
export const modal = {
  // Basic modals
  confirm: (config: Omit<ConfirmModalConfig, 'type'>) =>
    modalManager.confirm(config),

  alert: (config: Omit<AlertModalConfig, 'type'>) =>
    modalManager.alert(config),

  prompt: (config: Omit<PromptModalConfig, 'type'>) =>
    modalManager.prompt(config),

  loading: (config: Omit<LoadingModalConfig, 'type'>) =>
    modalManager.loading(config),

  // Quick alerts
  success: (message: string, options?: Partial<AlertModalConfig>) =>
    modalManager.success(message, options),

  error: (message: string, options?: Partial<AlertModalConfig>) =>
    modalManager.error(message, options),

  warning: (message: string, options?: Partial<AlertModalConfig>) =>
    modalManager.warning(message, options),

  info: (message: string, options?: Partial<AlertModalConfig>) =>
    modalManager.info(message, options),

  // Advanced modals
  form: (config: Omit<FormModalConfig, 'type'>) =>
    modalManager.form(config),

  image: (config: Omit<ImageModalConfig, 'type'>) =>
    modalManager.image(config),

  video: (config: Omit<VideoModalConfig, 'type'>) =>
    modalManager.video(config),

  custom: (config: Omit<CustomModalConfig, 'type'>) =>
    modalManager.custom(config),

  // Control methods
  close: (id: string) => modalManager.close(id),
  closeAll: () => modalManager.closeAll(),
  update: (id: string, updates: Partial<ModalConfig>) =>
    modalManager.update(id, updates),
}
