import type { modalManager } from './ModalContext'

export { BaseModal } from './BaseModal'

// Export context and hooks
export { modalManager, ModalProvider, useModal, useModalManager } from './ModalContext'

// Export components
export { <PERSON>dal<PERSON>enderer } from './ModalRenderer'
// Export individual modal components
export { AlertModal, ConfirmModal } from './modals/ConfirmModal'

export { CustomModal } from './modals/CustomModal'
export { FormModal } from './modals/FormModal'
export { LoadingModal } from './modals/LoadingModal'
export { ImageModal, VideoModal } from './modals/MediaModal'
export { PromptModal } from './modals/PromptModal'
// Export types
export type * from './types'

// Convenience function for global usage
export const modal = {
  // Basic modals
  confirm: (config: Omit<import('./types').ConfirmModalConfig, 'type'>) =>
    modalManager.confirm(config),

  alert: (config: Omit<import('./types').AlertModalConfig, 'type'>) =>
    modalManager.alert(config),

  prompt: (config: Omit<import('./types').PromptModalConfig, 'type'>) =>
    modalManager.prompt(config),

  loading: (config: Omit<import('./types').LoadingModalConfig, 'type'>) =>
    modalManager.loading(config),

  // Quick alerts
  success: (message: string, options?: Partial<import('./types').AlertModalConfig>) =>
    modalManager.success(message, options),

  error: (message: string, options?: Partial<import('./types').AlertModalConfig>) =>
    modalManager.error(message, options),

  warning: (message: string, options?: Partial<import('./types').AlertModalConfig>) =>
    modalManager.warning(message, options),

  info: (message: string, options?: Partial<import('./types').AlertModalConfig>) =>
    modalManager.info(message, options),

  // Advanced modals
  form: (config: Omit<import('./types').FormModalConfig, 'type'>) =>
    modalManager.form(config),

  image: (config: Omit<import('./types').ImageModalConfig, 'type'>) =>
    modalManager.image(config),

  video: (config: Omit<import('./types').VideoModalConfig, 'type'>) =>
    modalManager.video(config),

  custom: (config: Omit<import('./types').CustomModalConfig, 'type'>) =>
    modalManager.custom(config),

  // Control methods
  close: (id: string) => modalManager.close(id),
  closeAll: () => modalManager.closeAll(),
  update: (id: string, updates: Partial<import('./types').ModalConfig>) =>
    modalManager.update(id, updates),
}
