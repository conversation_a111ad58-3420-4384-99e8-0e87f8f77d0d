import React from 'react'
import { BaseModal } from '../BaseModal'
import type { CustomModalConfig } from '../types'

interface CustomModalProps {
  config: CustomModalConfig
  visible: boolean
  onClose: () => void
}

export const CustomModal: React.FC<CustomModalProps> = ({
  config,
  visible,
  onClose
}) => {
  const { component: Component, props = {} } = config

  return (
    <BaseModal config={config} visible={visible} onClose={onClose}>
      <Component {...props} onClose={onClose} />
    </BaseModal>
  )
}
